@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm-450, breakpoint-md-767, breakpoint-md, breakpoint-xl-1800 from breakpoints;

.image_hidden {
  display: none;
  transition: opacity 0.5s ease-in-out;
}

.image_visible {
  display: block;
}

.mobile_image {
  /* Specific styles for mobile images to prevent cutoff */
  @media screen and (max-width: 1023px) {
    object-fit: cover;
    object-position: center center;
    /* Ensure mobile images scale properly */
    width: 100% !important;
    height: 100% !important;
  }

  /* For mobile screens, adjust positioning to show more of the image */
  @media screen and (max-width: 767px) {
    object-fit: cover;
    object-position: center 20%;
  }
}
