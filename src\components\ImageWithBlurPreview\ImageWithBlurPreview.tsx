'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import classNames from '@utils/classNames';
import styles from './ImageWithBlurPreview.module.css';

export default function ImageWithBlurPreview({
  data,
  mobileData,
  width,
  height,
  fill,
  quality,
  priority,
  unoptimized,
  loading,
  mainClass,
  prefferedSize,
}: {
  data: any;
  mobileData?: any;
  width?: any;
  height?: any;
  fill?: any;
  unoptimized?: any;
  quality?: any;
  priority?: any;
  loading?: any;
  mainClass: any;
  prefferedSize?: 'large' | 'medium' | 'small';
}) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width <= 767);
      setIsTablet(width >= 768 && width <= 1023);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Determine which image data to use based on screen size and availability
  const getImageData = () => {
    // Use mobile image for mobile and tablet if available, otherwise fallback to desktop image
    if ((isMobile || isTablet) && mobileData) {
      return mobileData;
    }
    return data;
  };

  const imageData = getImageData();

  // Get thumbnail for blur preview - prefer mobile thumbnail if using mobile image
  const getThumbnailUrl = () => {
    if ((isMobile || isTablet) && mobileData?.formats?.thumbnail?.url) {
      return mobileData.formats.thumbnail.url;
    }
    return data?.formats?.thumbnail?.url;
  };

  return (
    <>
      {!isImageLoaded && (
        <Image
          src={getThumbnailUrl()}
          alt={imageData?.alternativeText || 'Hero image'}
          width={width}
          height={height}
          fill={fill}
          className={classNames(mainClass, 'blur')}
          priority={priority}
          loading={loading}
        />
      )}
      <Image
        src={
          prefferedSize === 'large'
            ? imageData?.format?.large?.url ||
              imageData?.formats?.large?.url ||
              imageData?.url
            : prefferedSize === 'medium'
              ? imageData?.format?.medium?.url ||
                imageData?.formats?.medium?.url ||
                imageData?.format?.large?.url ||
                imageData?.formats?.large?.url ||
                imageData?.url
              : prefferedSize === 'small'
                ? imageData?.format?.small?.url ||
                  imageData?.formats?.small?.url ||
                  imageData?.format?.medium?.url ||
                  imageData?.formats?.medium?.url ||
                  imageData?.format?.large?.url ||
                  imageData?.formats?.large?.url ||
                  imageData?.url
                : imageData?.url
        }
        width={width}
        height={height}
        fill={fill}
        alt={imageData?.alternativeText || 'Hero Image'}
        className={classNames(
          mainClass,
          isImageLoaded ? styles.image_visible : styles.image_hidden,
          (isMobile || isTablet) && mobileData ? styles.mobile_image : '',
        )}
        quality={quality}
        priority={priority}
        loading={loading}
        unoptimized={unoptimized}
        onLoad={() => setIsImageLoaded(true)}
      />
    </>
  );
}
