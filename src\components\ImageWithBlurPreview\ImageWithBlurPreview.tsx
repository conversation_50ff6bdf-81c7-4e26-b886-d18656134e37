'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import classNames from '@utils/classNames';
import styles from './ImageWithBlurPreview.module.css';

export default function ImageWithBlurPreview({
  data,
  mobileData,
  width,
  height,
  fill,
  quality,
  priority,
  unoptimized,
  loading,
  mainClass,
  prefferedSize,
}: {
  data: any;
  mobileData?: any;
  width?: any;
  height?: any;
  fill?: any;
  unoptimized?: any;
  quality?: any;
  priority?: any;
  loading?: any;
  mainClass: any;
  prefferedSize?: 'large' | 'medium' | 'small';
}) {
  const [isDesktopImageLoaded, setIsDesktopImageLoaded] = useState(false);
  const [isMobileImageLoaded, setIsMobileImageLoaded] = useState(false);

  return (
    <>
      {/* Desktop Image - Hidden on mobile/tablet when mobile image is available */}
      {!isDesktopImageLoaded && (
        <Image
          src={data?.formats?.thumbnail?.url}
          alt={data?.alternativeText || 'Hero image'}
          width={width}
          height={height}
          fill={fill}
          className={classNames(
            mainClass,
            'blur',
            mobileData ? styles.desktop_only : '',
          )}
          priority={priority}
          loading={loading}
        />
      )}
      <Image
        src={
          prefferedSize === 'large'
            ? data?.format?.large?.url || data?.formats?.large?.url || data?.url
            : prefferedSize === 'medium'
              ? data?.format?.medium?.url ||
                data?.formats?.medium?.url ||
                data?.format?.large?.url ||
                data?.formats?.large?.url ||
                data?.url
              : prefferedSize === 'small'
                ? data?.format?.small?.url ||
                  data?.formats?.small?.url ||
                  data?.format?.medium?.url ||
                  data?.formats?.medium?.url ||
                  data?.format?.large?.url ||
                  data?.formats?.large?.url ||
                  data?.url
                : data?.url
        }
        width={width}
        height={height}
        fill={fill}
        alt={data?.alternativeText || 'Hero Image'}
        className={classNames(
          mainClass,
          isDesktopImageLoaded ? styles.image_visible : styles.image_hidden,
          mobileData ? styles.desktop_only : '',
        )}
        quality={quality}
        priority={priority}
        loading={loading}
        unoptimized={unoptimized}
        onLoad={() => setIsDesktopImageLoaded(true)}
      />

      {/* Mobile Image - Only shown on mobile/tablet when available */}
      {mobileData && (
        <>
          {!isMobileImageLoaded && (
            <Image
              src={mobileData?.formats?.thumbnail?.url}
              alt={mobileData?.alternativeText || 'Mobile hero image'}
              width={width}
              height={height}
              fill={fill}
              className={classNames(
                mainClass,
                'blur',
                styles.mobile_tablet_only,
              )}
              priority={priority}
              loading={loading}
            />
          )}
          <Image
            src={
              prefferedSize === 'large'
                ? mobileData?.format?.large?.url ||
                  mobileData?.formats?.large?.url ||
                  mobileData?.url
                : prefferedSize === 'medium'
                  ? mobileData?.format?.medium?.url ||
                    mobileData?.formats?.medium?.url ||
                    mobileData?.format?.large?.url ||
                    mobileData?.formats?.large?.url ||
                    mobileData?.url
                  : prefferedSize === 'small'
                    ? mobileData?.format?.small?.url ||
                      mobileData?.formats?.small?.url ||
                      mobileData?.format?.medium?.url ||
                      mobileData?.formats?.medium?.url ||
                      mobileData?.format?.large?.url ||
                      mobileData?.formats?.large?.url ||
                      mobileData?.url
                    : mobileData?.url
            }
            width={width}
            height={height}
            fill={fill}
            alt={mobileData?.alternativeText || 'Mobile Hero Image'}
            className={classNames(
              mainClass,
              isMobileImageLoaded ? styles.image_visible : styles.image_hidden,
              styles.mobile_tablet_only,
              styles.mobile_image,
            )}
            quality={quality}
            priority={priority}
            loading={loading}
            unoptimized={unoptimized}
            onLoad={() => setIsMobileImageLoaded(true)}
          />
        </>
      )}
    </>
  );
}
