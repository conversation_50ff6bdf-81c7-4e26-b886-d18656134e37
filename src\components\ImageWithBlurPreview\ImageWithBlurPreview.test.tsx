import React from 'react';
import { render, screen } from '@testing-library/react';
import ImageWithBlurPreview from './ImageWithBlurPreview';

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />;
  };
});

// Mock window.innerWidth for responsive tests
const mockInnerWidth = (width: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  window.dispatchEvent(new Event('resize'));
};

const mockDesktopImageData = {
  url: 'https://example.com/desktop-image.jpg',
  alternativeText: 'Desktop image',
  formats: {
    thumbnail: {
      url: 'https://example.com/desktop-thumbnail.jpg',
    },
    large: {
      url: 'https://example.com/desktop-large.jpg',
    },
  },
};

const mockMobileImageData = {
  url: 'https://example.com/mobile-image.jpg',
  alternativeText: 'Mobile image',
  formats: {
    thumbnail: {
      url: 'https://example.com/mobile-thumbnail.jpg',
    },
    large: {
      url: 'https://example.com/mobile-large.jpg',
    },
  },
};

describe('ImageWithBlurPreview', () => {
  beforeEach(() => {
    // Reset to desktop size by default
    mockInnerWidth(1200);
  });

  it('renders desktop image on desktop screen size', () => {
    mockInnerWidth(1200);
    
    render(
      <ImageWithBlurPreview
        data={mockDesktopImageData}
        mobileData={mockMobileImageData}
        mainClass="test-class"
      />
    );

    // Should use desktop image URL
    const images = screen.getAllByAltText('Desktop image');
    expect(images.length).toBeGreaterThan(0);
  });

  it('renders mobile image on mobile screen size when available', () => {
    mockInnerWidth(600);
    
    render(
      <ImageWithBlurPreview
        data={mockDesktopImageData}
        mobileData={mockMobileImageData}
        mainClass="test-class"
      />
    );

    // Should use mobile image URL
    const images = screen.getAllByAltText('Mobile image');
    expect(images.length).toBeGreaterThan(0);
  });

  it('renders mobile image on tablet screen size when available', () => {
    mockInnerWidth(800);
    
    render(
      <ImageWithBlurPreview
        data={mockDesktopImageData}
        mobileData={mockMobileImageData}
        mainClass="test-class"
      />
    );

    // Should use mobile image URL for tablet
    const images = screen.getAllByAltText('Mobile image');
    expect(images.length).toBeGreaterThan(0);
  });

  it('falls back to desktop image when mobile image is not available', () => {
    mockInnerWidth(600);
    
    render(
      <ImageWithBlurPreview
        data={mockDesktopImageData}
        mobileData={null}
        mainClass="test-class"
      />
    );

    // Should fallback to desktop image
    const images = screen.getAllByAltText('Desktop image');
    expect(images.length).toBeGreaterThan(0);
  });

  it('renders with preferred size large', () => {
    render(
      <ImageWithBlurPreview
        data={mockDesktopImageData}
        mainClass="test-class"
        prefferedSize="large"
      />
    );

    const images = screen.getAllByAltText('Desktop image');
    expect(images.length).toBeGreaterThan(0);
  });
});
